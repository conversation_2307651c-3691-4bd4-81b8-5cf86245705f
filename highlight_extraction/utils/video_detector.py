#!/usr/bin/env python3
"""
Video Highlights Detection System

Advanced highlight detection algorithm designed to find the most engaging,
valuable, and shareable moments in any video content.

Core Philosophy:
- Content quality over keyword matching
- Engagement potential over technical metrics
- Simplicity over complexity
- Universal applicability over niche optimization

Algorithm:
1. Content Analysis: Analyze transcript for engagement signals
2. Momentum Detection: Find moments of high information density
3. Emotional Peaks: Identify emotional highs and compelling moments
4. Natural Boundaries: Respect speech patterns and natural breaks
5. Optimal Selection: Choose best moments with diversity and flow
"""

import re
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import statistics

logger = logging.getLogger(__name__)

# Import validation utilities for consistent minimum duration enforcement
try:
    from utils.validation_utils import HighlightValidationUtils
    from config.settings import MIN_HIGHLIGHT_DURATION_SECONDS
except ImportError:
    # Fallback if imports fail
    HighlightValidationUtils = None
    MIN_HIGHLIGHT_DURATION_SECONDS = 10.0

class VideoHighlightsDetector:
    """
    Advanced video highlights detection system.

    Finds the most engaging moments in any video by analyzing:
    - Speech patterns and energy
    - Information density and value
    - Emotional engagement signals
    - Natural conversation flow
    - Content uniqueness and impact
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

        # Core parameters for finding optimal scenes
        self.min_highlight_duration = 10.0  # Minimum 10 seconds for impact
        self.max_highlight_duration = 30.0  # Maximum 30 seconds for attention span
        self.target_total_duration = 300.0  # Target 5 minutes of highlights (much more generous)
        self.overlap_threshold = 1.0        # Minimum gap between highlights (allow closer spacing)

        # Engagement signal patterns (advanced detection algorithms)
        self.engagement_patterns = {
            # High-value question words that create curiosity
            'curiosity_triggers': [
                'what if', 'imagine', 'picture this', 'here\'s the thing',
                'the secret', 'the truth', 'nobody tells you', 'most people don\'t',
                'the real reason', 'what really happens', 'the problem is',
                'insight', 'we think', 'you can\'t', 'but we can', 'exactly',
                'i think', 'to add to that', 'the fastest way', 'in fact',
                'you know', 'the way that', 'they realize', 'the number one',
                'i heard', 'i like the story', 'where they', 'what they do'
            ],

            # Emotional intensity indicators
            'emotional_peaks': [
                'incredible', 'amazing', 'unbelievable', 'shocking', 'surprising',
                'devastating', 'heartbreaking', 'hilarious', 'terrifying',
                'mind-blowing', 'life-changing', 'game-changing'
            ],

            # Authority and credibility signals
            'authority_signals': [
                'research shows', 'studies prove', 'data reveals', 'experts say',
                'i discovered', 'i learned', 'i realized', 'the fact is',
                'what we found', 'the results show', 'absolutely', 'alanda barton says',
                'dale carnegie', 'michelle thomas', 'jeffrey miller', 'famous thread',
                'reddit', 'bore has famously wrote', 'my cousin said', 'taleb'
            ],

            # Story and narrative hooks
            'story_hooks': [
                'let me tell you', 'here\'s what happened', 'i remember when',
                'there was this time', 'picture this', 'imagine you\'re',
                'so there i was', 'it all started when'
            ],

            # Conflict and tension builders
            'tension_builders': [
                'but here\'s the problem', 'the issue is', 'what went wrong',
                'the mistake', 'the failure', 'the challenge', 'the struggle',
                'but then', 'suddenly', 'unexpectedly'
            ],

            # Resolution and insight moments
            'insight_moments': [
                'that\'s when i realized', 'the breakthrough came', 'it clicked',
                'suddenly it made sense', 'the answer was', 'i figured out',
                'the solution is', 'here\'s the key', 'change', 'relationship',
                'love', 'connection', 'unity', 'wholeness', 'decision', 'choice',
                'heuristic', 'path', 'painful', 'equanimous', 'peace', 'mental peace',
                'iterate', 'leverage', 'opportunities', 'friend circle', 'dating pool',
                'job opportunities', 'quality of life', 'genetics', 'behavior',
                'temperament', 'values', 'compromise', 'loss aversion', 'starting over',
                'successful people', 'mountain climbing', 'trauma', 'insight',
                'schedule', 'alienate', 'self-conscious', 'compliment', 'criticize',
                'confidence', 'passive observer', 'objective', 'threatened', 'fearful',
                'praiseworthy', 'authenticity', 'potential', 'resume', 'basis',
                'ineffable', 'consciousness', 'desire for unity', 'god-shaped hole',
                'mysticism', 'awe', 'sistine chapel', 'emotion', 'craves'
            ]
        }

        # Speech energy indicators
        self.energy_indicators = {
            'high_energy': ['!', 'really', 'absolutely', 'definitely', 'exactly', 'totally'],
            'emphasis': ['very', 'extremely', 'incredibly', 'massively', 'hugely'],
            'certainty': ['always', 'never', 'every', 'all', 'none', 'everything', 'nothing']
        }

        self.logger.info("Advanced Highlights Detector initialized")

    def find_best_highlights(self, transcript_segments: List[Dict[str, Any]],
                           video_duration: Optional[float] = None,
                           max_highlights: Optional[int] = None,
                           min_duration_override: Optional[float] = None,
                           max_duration_override: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Find the optimal highlights in the video.

        Args:
            transcript_segments: List of transcript segments with timing
            video_duration: Total video duration (optional)
            max_highlights: Maximum number of highlights to generate (optional, defaults to 100 for production, 1 for testing)
            min_duration_override: Optional override for minimum highlight duration.
            max_duration_override: Optional override for maximum highlight duration.

        Returns:
            List of the optimal highlight segments with scores and metadata
        """
        if not transcript_segments:
            return []

        # Store original durations and apply overrides if provided
        original_min_highlight_duration = self.min_highlight_duration
        original_max_highlight_duration = self.max_highlight_duration

        if min_duration_override is not None:
            self.min_highlight_duration = float(min_duration_override)
            self.logger.info(f"Overriding min_highlight_duration to: {self.min_highlight_duration}s")
        if max_duration_override is not None:
            self.max_highlight_duration = float(max_duration_override)
            self.logger.info(f"Overriding max_highlight_duration to: {self.max_highlight_duration}s")

        # Ensure min_highlight_duration is used consistently
        self.logger.info(f"Using min_highlight_duration: {self.min_highlight_duration}s for this run.")

        if max_highlights is not None:
            if max_highlights == 1:
                self.logger.info(f"🧪 Testing mode: Analyzing {len(transcript_segments)} segments to find the single optimal highlight")
            elif max_highlights <= 3:
                self.logger.info(f"🧪 Testing mode: Analyzing {len(transcript_segments)} segments for up to {max_highlights} highlights")
            else:
                self.logger.info(f"Analyzing {len(transcript_segments)} segments for advanced highlights (max: {max_highlights})")
        else:
            self.logger.info(f"Analyzing {len(transcript_segments)} segments for advanced highlights")

        # Step 1: Analyze each segment for engagement potential
        segment_scores = self._analyze_segment_engagement(transcript_segments)

        # Step 2: Find momentum peaks (high information density moments)
        momentum_peaks = self._find_momentum_peaks(transcript_segments, segment_scores)

        # Step 3: Detect emotional and narrative peaks
        narrative_peaks = self._find_narrative_peaks(transcript_segments)

        # Step 4: Generate candidate highlight windows
        candidates = self._generate_highlight_candidates(
            transcript_segments, segment_scores, momentum_peaks, narrative_peaks
        )

        # Step 5: Score and rank all candidates
        scored_candidates = self._score_highlight_candidates(candidates, transcript_segments)

        # Step 6: Select optimal set of non-overlapping highlights
        final_highlights = self._select_optimal_highlights(scored_candidates, max_highlights)

        self.logger.info(f"Selected {len(final_highlights)} advanced highlights "
                        f"({sum(h['duration'] for h in final_highlights):.1f}s total)")

        # Restore original durations
        self.min_highlight_duration = original_min_highlight_duration
        self.max_highlight_duration = original_max_highlight_duration

        return final_highlights
